import { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Heart, MessageSquare, Share2, Upload } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { PhotoUploadModal } from "@/components/PhotoUploadModal";
import { CommentSection } from "@/components/CommentSection";

import { useAuth } from "@/contexts/AuthContext";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

interface Comment {
  id: string;
  user: {
    name: string;
    avatar: string;
    initials: string;
  };
  text: string;
  timestamp: string;
}

interface PhotoPost {
  id: string;
  user: {
    name: string;
    avatar: string;
    initials: string;
  };
  tour: string;
  image: string;
  caption: string;
  likes: number;
  comments: Comment[];
  commentCount: number;
  timestamp: string;
  isLiked: boolean;
}

const PhotoAlbum = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [commentingOnPost, setCommentingOnPost] = useState<string | null>(null);

  // Fetch posts from database
  const { data: posts = [], isLoading } = useQuery({
    queryKey: ["posts"],
    queryFn: async () => {
      const response = await fetch("/api/posts");
      if (!response.ok) {
        throw new Error("Failed to fetch posts");
      }
      const postsData = await response.json();

      return postsData.map((post: any) => ({
        id: post.id,
        user: {
          name:
            post.profile?.displayName || post.profile?.username || "Anonymous",
          avatar:
            post.profile?.avatarUrl ||
            `https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face`,
          initials: (post.profile?.displayName || post.profile?.username || "A")
            .charAt(0)
            .toUpperCase(),
        },
        tour: post.tour,
        image: post.imageUrl,
        caption: post.caption,
        likes: post._count?.likes || 0,
        comments:
          post.comments?.map((comment: any) => ({
            id: comment.id,
            user: {
              name:
                comment.profile?.displayName ||
                comment.profile?.username ||
                "Anonymous",
              avatar:
                comment.profile?.avatarUrl ||
                `https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face`,
              initials: (
                comment.profile?.displayName ||
                comment.profile?.username ||
                "A"
              )
                .charAt(0)
                .toUpperCase(),
            },
            text: comment.text,
            timestamp: new Date(comment.createdAt).toLocaleDateString(),
          })) || [],
        commentCount: post._count?.comments || 0,
        timestamp: new Date(post.createdAt).toLocaleDateString(),
        isLiked: false, // TODO: Implement like checking
      }));
    },
    enabled: !!user,
  });

  // Toggle like mutation
  const toggleLikeMutation = useMutation({
    mutationFn: async ({
      postId,
      isLiked,
    }: {
      postId: string;
      isLiked: boolean;
    }) => {
      if (isLiked) {
        // Remove like
        // For now, just simulate the like toggle
        // TODO: Implement like API endpoints
        return Promise.resolve();
      } else {
        // For now, just simulate the like toggle
        // TODO: Implement like API endpoints
        return Promise.resolve();
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["posts"] });
    },
  });

  const toggleLike = (postId: string) => {
    if (!user) {
      toast({
        title: "Please log in",
        description: "You need to be logged in to like posts",
        variant: "destructive",
      });
      return;
    }

    const post = posts.find((p) => p.id === postId);
    if (post) {
      toggleLikeMutation.mutate({ postId, isLiked: post.isLiked });
    }
  };

  const handleShare = (postId: string) => {
    toast({
      title: "Shared!",
      description: "Post shared successfully",
    });
  };

  // Add comment mutation
  const addCommentMutation = useMutation({
    mutationFn: async ({ postId, text }: { postId: string; text: string }) => {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/comments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ postId, text }),
      });

      if (!response.ok) {
        throw new Error("Failed to add comment");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["posts"] });
    },
  });

  const handleAddComment = (postId: string, newComment: Comment) => {
    if (!user) {
      toast({
        title: "Please log in",
        description: "You need to be logged in to comment",
        variant: "destructive",
      });
      return;
    }

    addCommentMutation.mutate({ postId, text: newComment.text });
  };

  const handleUpload = () => {
    setIsUploadModalOpen(true);
  };

  const handlePhotoUpload = () => {
    queryClient.invalidateQueries({ queryKey: ["posts"] });
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Travel Photo Album
          </h1>
          <p className="text-xl text-muted-foreground mb-6">
            Share your amazing Tanzania experiences with fellow travelers
          </p>
          <Button onClick={handleUpload} className="mb-6">
            <Upload className="w-4 h-4 mr-2" />
            Upload Your Photos
          </Button>
        </div>

        <div className="space-y-6">
          {isLoading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Loading posts...</p>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                No posts yet. Be the first to share your travel photos!
              </p>
            </div>
          ) : (
            posts.map((post) => (
              <Card key={post.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage
                        src={post.user.avatar}
                        alt={post.user.name}
                      />
                      <AvatarFallback>{post.user.initials}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <h3 className="font-semibold text-foreground">
                        {post.user.name}
                      </h3>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {post.tour}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {post.timestamp}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <div className="aspect-[4/3] overflow-hidden">
                  <img
                    src={post.image}
                    alt="Travel photo"
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>

                <CardContent className="pt-4">
                  <p className="text-foreground mb-4">{post.caption}</p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => toggleLike(post.id)}
                        className="flex items-center space-x-1 hover:text-red-500 transition-colors"
                      >
                        <Heart
                          className={`w-5 h-5 ${
                            post.isLiked
                              ? "fill-red-500 text-red-500"
                              : "text-muted-foreground"
                          }`}
                        />
                        <span className="text-sm font-medium">
                          {post.likes}
                        </span>
                      </button>

                      <div
                        className="flex items-center space-x-1 text-muted-foreground cursor-pointer hover:text-foreground transition-colors"
                        onClick={() =>
                          setCommentingOnPost(
                            commentingOnPost === post.id ? null : post.id
                          )
                        }
                      >
                        <MessageSquare className="w-5 h-5" />
                        <span className="text-sm">{post.commentCount}</span>
                      </div>
                    </div>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleShare(post.id)}
                    >
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>

                <CardFooter className="pt-0">
                  <CommentSection
                    postId={post.id}
                    comments={post.comments}
                    onAddComment={handleAddComment}
                    isVisible={commentingOnPost === post.id}
                  />
                </CardFooter>
              </Card>
            ))
          )}
        </div>
      </div>

      <PhotoUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onUpload={handlePhotoUpload}
      />
    </div>
  );
};

export default PhotoAlbum;
