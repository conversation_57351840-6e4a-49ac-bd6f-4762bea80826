import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>oot<PERSON>,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCart } from "@/contexts/CartContext";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";

import { AddProductModal } from "@/components/AddProductModal";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  price_type: string;
  price_min: number;
  price_max: number | null;
  category: string;
  image_url: string;
  in_stock: boolean;
  seller_id: string;
  discount_percentage: number;
  promotion_text: string | null;
  promotion_expires_at: string | null;
}

const Shop = () => {
  const { toast } = useToast();
  const { addToCart } = useCart();
  const { user } = useAuth();
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);

  useEffect(() => {
    fetchProducts();
    if (user) {
      fetchUserProfile();
    }
  }, [user]);

  const fetchProducts = async () => {
    try {
      const response = await fetch("/api/products");
      if (!response.ok) {
        throw new Error("Failed to fetch products");
      }
      const data = await response.json();
      setProducts(data || []);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast({
        title: "Error",
        description: "Failed to load products",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserProfile = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/profiles", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUserProfile(data);
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  const handleAddToCart = (product: Product) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image_url,
    });
    toast({
      title: "Added to Cart",
      description: `${product.name} has been added to your cart`,
    });
  };

  const handleAddProductClick = async () => {
    if (!user) {
      // Redirect to login if not authenticated
      router.push("/auth");
      return;
    }

    // Check if user is a seller, if not, make them a seller
    if (!userProfile?.isSeller) {
      try {
        // For now, just update local state - implement API endpoint later
        setUserProfile({ ...userProfile, isSeller: true });

        toast({
          title: "Seller Account Activated",
          description: "You can now add products to the shop!",
        });
      } catch (error) {
        console.error("Error updating seller status:", error);
        toast({
          title: "Error",
          description: "Failed to activate seller account. Please try again.",
          variant: "destructive",
        });
        return;
      }
    }

    setIsAddModalOpen(true);
  };

  const handleProductAdded = () => {
    fetchProducts();
    setIsAddModalOpen(false);
    toast({
      title: "Product Added",
      description: "Your product has been added to the shop",
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-4xl font-bold text-foreground mb-4">
                Tanzania Souvenir Shop
              </h1>
              <p className="text-xl text-muted-foreground max-w-2xl">
                Take home authentic Tanzanian crafts, local products, and
                memorable souvenirs
              </p>
            </div>
            <Button
              onClick={() => handleAddProductClick()}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Product
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <div className="text-lg text-muted-foreground">
              Loading products...
            </div>
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold mb-2">
              No products available
            </h3>
            <p className="text-muted-foreground">
              Be the first to add a product to the shop!
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <Card
                key={product.id}
                className="group hover:shadow-lg transition-all duration-300 hover-scale"
              >
                <div className="aspect-square overflow-hidden rounded-t-lg relative">
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  {!product.in_stock && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <Badge variant="destructive">Out of Stock</Badge>
                    </div>
                  )}
                </div>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <Badge variant="secondary" className="mb-2">
                      {product.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg">{product.name}</CardTitle>
                </CardHeader>
                <CardContent className="pb-3">
                  <CardDescription className="text-sm mb-3 line-clamp-2">
                    {product.description}
                  </CardDescription>

                  {/* Price Display */}
                  <div className="mb-3">
                    {product.price_type === "range" ? (
                      <div className="text-lg font-bold text-primary">
                        ${product.price_min} - ${product.price_max}
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <div
                          className={`text-lg font-bold ${
                            product.discount_percentage > 0
                              ? "text-muted-foreground line-through"
                              : "text-primary"
                          }`}
                        >
                          ${product.price}
                        </div>
                        {product.discount_percentage > 0 && (
                          <div className="text-lg font-bold text-primary">
                            $
                            {(
                              product.price *
                              (1 - product.discount_percentage / 100)
                            ).toFixed(2)}
                          </div>
                        )}
                      </div>
                    )}

                    {/* Discount Badge */}
                    {product.discount_percentage > 0 && (
                      <Badge variant="destructive" className="mt-1">
                        {product.discount_percentage}% OFF
                      </Badge>
                    )}
                  </div>

                  {/* Promotion Text */}
                  {product.promotion_text &&
                    product.promotion_expires_at &&
                    new Date(product.promotion_expires_at) > new Date() && (
                      <div className="mb-3">
                        <Badge
                          variant="secondary"
                          className="bg-orange-100 text-orange-800"
                        >
                          🎉 {product.promotion_text}
                        </Badge>
                      </div>
                    )}
                </CardContent>
                <CardFooter className="pt-0">
                  <Button
                    onClick={() => handleAddToCart(product)}
                    disabled={!product.in_stock}
                    className="w-full"
                    size="sm"
                  >
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    {product.in_stock ? "Add to Cart" : "Out of Stock"}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}

        <AddProductModal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          onProductAdded={handleProductAdded}
        />
      </div>
    </div>
  );
};

export default Shop;
