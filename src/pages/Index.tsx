import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { Badge } from "@/components/ui/badge";
import { Star, MapPin, Clock, Users } from "lucide-react";

const Index = () => {
  const { user } = useAuth();
  const featuredTours = [
    {
      id: "1",
      title: "Serengeti Safari Adventure",
      description:
        "Experience the magnificent wildlife of Serengeti National Park",
      image:
        "https://images.unsplash.com/photo-1466721591366-2d5fba72006d?w=600&h=400&fit=crop",
      rating: 4.8,
      reviews: 245,
      price: 1200,
      duration: "5 days",
    },
    {
      id: "2",
      title: "Mount Kilimanjaro Trek",
      description: "Climb Africa's highest peak with experienced guides",
      image:
        "https://images.unsplash.com/photo-1426604966848-d7adac402bff?w=600&h=400&fit=crop",
      rating: 4.9,
      reviews: 189,
      price: 2500,
      duration: "7 days",
    },
    {
      id: "3",
      title: "Zanzibar Beach Paradise",
      description: "Relax on pristine beaches and explore Stone Town",
      image:
        "https://images.unsplash.com/photo-1500673922987-e212871fec22?w=600&h=400&fit=crop",
      rating: 4.7,
      reviews: 156,
      price: 800,
      duration: "4 days",
    },
  ];

  const stats = [
    { label: "Happy Travelers", value: "10,000+", icon: Users },
    { label: "Tours Available", value: "50+", icon: MapPin },
    { label: "Years Experience", value: "15+", icon: Clock },
    { label: "Average Rating", value: "4.8", icon: Star },
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-primary/10 to-secondary/10 py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-foreground mb-6 animate-fade-in">
            Discover the Magic of
            <span className="text-primary block">Tanzania</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8 animate-fade-in">
            From the vast plains of Serengeti to the pristine beaches of
            Zanzibar, embark on unforgettable adventures in one of Africa's most
            beautiful countries
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in">
            <Link href="/tours">
              <Button size="lg" className="hover-scale">
                Explore Tours
              </Button>
            </Link>
            <Link href="/album">
              <Button variant="outline" size="lg" className="hover-scale">
                View Photo Album
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center animate-fade-in">
                <stat.icon className="w-8 h-8 mx-auto mb-3 text-primary" />
                <div className="text-3xl font-bold text-foreground mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Tours */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Featured Tours
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover our most popular Tanzania adventures, carefully curated
              for unforgettable experiences
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredTours.map((tour) => (
              <Card
                key={tour.id}
                className="group hover:shadow-xl transition-all duration-300 hover-scale overflow-hidden"
              >
                <div className="aspect-[4/3] overflow-hidden">
                  <img
                    src={tour.image}
                    alt={tour.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary">{tour.duration}</Badge>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{tour.rating}</span>
                      <span className="text-sm text-muted-foreground">
                        ({tour.reviews})
                      </span>
                    </div>
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors">
                    {tour.title}
                  </CardTitle>
                  <CardDescription>{tour.description}</CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex justify-between items-center">
                    <div className="text-2xl font-bold text-primary">
                      ${tour.price}
                    </div>
                    <Link href="/tours">
                      <Button size="sm" className="hover-scale">
                        Learn More
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link href="/tours">
              <Button size="lg" variant="outline" className="hover-scale">
                View All Tours
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">
              Why Choose Tanzania Tours?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center hover:shadow-lg transition-all duration-300 hover-scale">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MapPin className="w-6 h-6 text-primary" />
                </div>
                <CardTitle className="text-lg">Expert Tours</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Professionally guided tours with local expertise
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-all duration-300 hover-scale">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-primary" />
                </div>
                <CardTitle className="text-lg">Community</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Share experiences with fellow travelers
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-all duration-300 hover-scale">
              <CardHeader>
                <Link href="/shop">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-primary font-bold">🛍️</span>
                  </div>
                  <CardTitle className="text-lg">Local Shopping</CardTitle>
                </Link>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Authentic Tanzanian crafts and souvenirs
                </p>
              </CardContent>
            </Card>

            <Card className="text-center hover:shadow-lg transition-all duration-300 hover-scale">
              <CardHeader>
                <Link href="/reviews">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Star className="w-6 h-6 text-primary" />
                  </div>
                  <CardTitle className="text-lg">Reviews</CardTitle>
                </Link>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Read and share authentic travel experiences
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
