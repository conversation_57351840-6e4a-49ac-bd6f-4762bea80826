import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>oot<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Book, Album } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { BookingModal } from "@/components/BookingModal";
import { TourAlbumModal } from "@/components/TourAlbumModal";

interface Tour {
  id: string;
  title: string;
  description: string;
  price: number;
  duration: string;
  location: string;
  rating: number;
  reviews: number;
  image: string;
  category: string;
}

const Tours = () => {
  const { toast } = useToast();
  const [selectedTour, setSelectedTour] = useState<Tour | null>(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [tours] = useState<Tour[]>([
    {
      id: "1",
      title: "Serengeti Safari Adventure",
      description: "Experience the magnificent wildlife of Serengeti National Park with our expert guides.",
      price: 1200,
      duration: "5 days",
      location: "Serengeti, Tanzania",
      rating: 4.8,
      reviews: 245,
      image: "https://images.unsplash.com/photo-1466721591366-2d5fba72006d?w=400&h=300&fit=crop",
      category: "Safari"
    },
    {
      id: "2",
      title: "Mount Kilimanjaro Trek",
      description: "Climb Africa's highest peak with experienced mountaineering guides.",
      price: 2500,
      duration: "7 days",
      location: "Kilimanjaro, Tanzania",
      rating: 4.9,
      reviews: 189,
      image: "https://images.unsplash.com/photo-1426604966848-d7adac402bff?w=400&h=300&fit=crop",
      category: "Adventure"
    },
    {
      id: "3",
      title: "Zanzibar Beach Paradise",
      description: "Relax on pristine beaches and explore the rich culture of Stone Town.",
      price: 800,
      duration: "4 days",
      location: "Zanzibar, Tanzania",
      rating: 4.7,
      reviews: 156,
      image: "https://images.unsplash.com/photo-1500673922987-e212871fec22?w=400&h=300&fit=crop",
      category: "Beach"
    },
    {
      id: "4",
      title: "Ngorongoro Crater Safari",
      description: "Discover the world's largest volcanic caldera and its incredible wildlife.",
      price: 950,
      duration: "3 days",
      location: "Ngorongoro, Tanzania",
      rating: 4.6,
      reviews: 134,
      image: "https://images.unsplash.com/photo-1472396961693-142e6e269027?w=400&h=300&fit=crop",
      category: "Safari"
    }
  ]);

  const handleBookTour = (tour: Tour) => {
    setSelectedTour(tour);
    setIsBookingModalOpen(true);
  };

  const handleCloseBookingModal = () => {
    setIsBookingModalOpen(false);
    setSelectedTour(null);
  };

  const [selectedTourForAlbum, setSelectedTourForAlbum] = useState<Tour | null>(null);
  const [isAlbumModalOpen, setIsAlbumModalOpen] = useState(false);

  const handleViewAlbum = (tourId: string, tourTitle: string) => {
    const tour = tours.find(t => t.id === tourId);
    if (tour) {
      setSelectedTourForAlbum(tour);
      setIsAlbumModalOpen(true);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Discover Tanzania's Wonders
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            From the majestic Serengeti to the heights of Kilimanjaro, explore the most beautiful destinations in Tanzania
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {tours.map((tour) => (
            <Card key={tour.id} className="group hover:shadow-lg transition-all duration-300 hover-scale">
              <div className="aspect-[4/3] overflow-hidden rounded-t-lg">
                <img
                  src={tour.image}
                  alt={tour.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <Badge variant="secondary" className="mb-2">
                    {tour.category}
                  </Badge>
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">{tour.rating}</span>
                    <span className="text-sm text-muted-foreground">({tour.reviews})</span>
                  </div>
                </div>
                <CardTitle className="text-lg">{tour.title}</CardTitle>
                <CardDescription className="text-sm">{tour.location}</CardDescription>
              </CardHeader>
              <CardContent className="pb-3">
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {tour.description}
                </p>
                <div className="flex justify-between items-center">
                  <div className="text-lg font-bold text-primary">
                    ${tour.price}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {tour.duration}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex gap-2 pt-0">
                <Button 
                  onClick={() => handleBookTour(tour)}
                  className="flex-1"
                  size="sm"
                >
                  <Book className="w-4 h-4 mr-1" />
                  Book Now
                </Button>
                <Button 
                  onClick={() => handleViewAlbum(tour.id, tour.title)}
                  variant="outline"
                  size="sm"
                >
                  <Album className="w-4 h-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>

      <BookingModal 
        tour={selectedTour}
        isOpen={isBookingModalOpen}
        onClose={handleCloseBookingModal}
      />

      <TourAlbumModal 
        tour={selectedTourForAlbum}
        isOpen={isAlbumModalOpen}
        onClose={() => setIsAlbumModalOpen(false)}
      />
    </div>
  );
};

export default Tours;