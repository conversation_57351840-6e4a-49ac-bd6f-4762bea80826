import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";

interface Profile {
  id: string;
  userId: string;
  username: string | null;
  displayName: string | null;
  avatarUrl: string | null;
  bio: string | null;
  role: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthUser {
  id: string;
  email: string;
  profile?: Profile;
}

interface AuthContextType {
  user: AuthUser | null;
  login: (email: string, password: string) => Promise<{ error?: any }>;
  signup: (
    email: string,
    password: string,
    metadata?: any
  ) => Promise<{ error?: any }>;
  resetPassword: (email: string) => Promise<{ error?: any }>;
  logout: () => Promise<void>;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const getStoredToken = () => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("auth_token");
    }
    return null;
  };

  const setStoredToken = (token: string) => {
    if (typeof window !== "undefined") {
      localStorage.setItem("auth_token", token);
    }
  };

  const removeStoredToken = () => {
    if (typeof window !== "undefined") {
      localStorage.removeItem("auth_token");
    }
  };

  useEffect(() => {
    const checkAuth = async () => {
      const token = getStoredToken();
      if (token) {
        try {
          const response = await fetch("/api/auth/me", {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          });

          if (response.ok) {
            const data = await response.json();
            setUser(data.user);
          } else {
            removeStoredToken();
          }
        } catch (error) {
          console.error("Auth check error:", error);
          removeStoredToken();
        }
      }
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        setIsLoading(false);
        return { error: data.error || "Login failed" };
      }

      setStoredToken(data.token);
      setUser(data.user);
      setIsLoading(false);
      return { error: null };
    } catch (error) {
      setIsLoading(false);
      return { error: "Network error" };
    }
  };

  const signup = async (email: string, password: string, metadata?: any) => {
    setIsLoading(true);

    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          username: metadata?.username,
          display_name: metadata?.display_name,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        setIsLoading(false);
        return { error: data.error || "Signup failed" };
      }

      setStoredToken(data.token);
      setUser(data.user);
      setIsLoading(false);
      return { error: null };
    } catch (error) {
      setIsLoading(false);
      return { error: "Network error" };
    }
  };

  const resetPassword = async (email: string) => {
    try {
      // For now, just return success - implement email reset later
      return { error: null };
    } catch (error) {
      return { error: "Reset password not implemented yet" };
    }
  };

  const logout = async () => {
    try {
      removeStoredToken();
      setUser(null);
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        signup,
        resetPassword,
        logout,
        isLoading,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
