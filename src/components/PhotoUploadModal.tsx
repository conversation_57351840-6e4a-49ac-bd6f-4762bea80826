import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Upload, X, Image as ImageIcon, Share2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

import { useAuth } from "@/contexts/AuthContext";

interface PhotoUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: () => void;
}

export const PhotoUploadModal = ({
  isOpen,
  onClose,
  onUpload,
}: PhotoUploadModalProps) => {
  const { toast } = useToast();
  const { user } = useAuth();

  const [uploadData, setUploadData] = useState({
    tour: "",
    caption: "",
    location: "",
    selectedFile: null as File | null,
    previewUrl: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        // 5MB limit
        toast({
          title: "File too large",
          description: "Please select an image smaller than 5MB",
          variant: "destructive",
        });
        return;
      }

      if (!file.type.startsWith("image/")) {
        toast({
          title: "Invalid file type",
          description: "Please select an image file",
          variant: "destructive",
        });
        return;
      }

      setUploadData({
        ...uploadData,
        selectedFile: file,
        previewUrl: URL.createObjectURL(file),
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      window.location.href = "/auth";
      return;
    }

    if (!uploadData.selectedFile || !uploadData.tour || !uploadData.caption) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields and select an image",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const token = localStorage.getItem("auth_token");
      const response = await fetch("/api/posts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          tour: uploadData.tour,
          imageUrl: uploadData.previewUrl,
          caption: uploadData.caption,
          location: uploadData.location || null,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to upload photo");
      }

      toast({
        title: "Photo Uploaded!",
        description: "Your photo has been shared with the community",
      });

      // Reset form and close modal
      setUploadData({
        tour: "",
        caption: "",
        location: "",
        selectedFile: null,
        previewUrl: "",
      });
      onUpload();
      onClose();
    } catch (error) {
      console.error("Error uploading photo:", error);
      toast({
        title: "Upload Failed",
        description: "Failed to upload your photo. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const removeImage = () => {
    if (uploadData.previewUrl) {
      URL.revokeObjectURL(uploadData.previewUrl);
    }
    setUploadData({
      ...uploadData,
      selectedFile: null,
      previewUrl: "",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="text-2xl">
            Share Your Travel Photo
          </DialogTitle>
          <DialogDescription>
            Upload a photo from your Tanzania adventure and share your
            experience with other travelers
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto pr-2">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Photo Upload Area */}
            <div className="space-y-4">
              <Label>Photo *</Label>
              {!uploadData.previewUrl ? (
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8">
                  <div className="text-center">
                    <ImageIcon className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
                    <div className="space-y-2">
                      <Label htmlFor="photo-upload" className="cursor-pointer">
                        <div className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                          <Upload className="w-4 h-4" />
                          Choose Photo
                        </div>
                      </Label>
                      <Input
                        id="photo-upload"
                        type="file"
                        accept="image/*"
                        onChange={handleFileSelect}
                        className="hidden"
                      />
                      <p className="text-sm text-muted-foreground">
                        Drag and drop or click to upload (Max 5MB)
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <img
                    src={uploadData.previewUrl}
                    alt="Preview"
                    className="w-full h-64 object-cover rounded-lg"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={removeImage}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>

            {/* Form Fields */}
            <div>
              <Label htmlFor="tour">Tour/Experience *</Label>
              <Select
                value={uploadData.tour}
                onValueChange={(value) =>
                  setUploadData({ ...uploadData, tour: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select tour" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Serengeti Safari Adventure">
                    Serengeti Safari Adventure
                  </SelectItem>
                  <SelectItem value="Mount Kilimanjaro Trek">
                    Mount Kilimanjaro Trek
                  </SelectItem>
                  <SelectItem value="Zanzibar Beach Paradise">
                    Zanzibar Beach Paradise
                  </SelectItem>
                  <SelectItem value="Ngorongoro Crater Safari">
                    Ngorongoro Crater Safari
                  </SelectItem>
                  <SelectItem value="Other">Other Experience</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="location">Location (Optional)</Label>
              <Input
                id="location"
                value={uploadData.location}
                onChange={(e) =>
                  setUploadData({ ...uploadData, location: e.target.value })
                }
                placeholder="Where was this photo taken?"
              />
            </div>

            <div>
              <Label htmlFor="caption">Caption *</Label>
              <Textarea
                id="caption"
                value={uploadData.caption}
                onChange={(e) =>
                  setUploadData({ ...uploadData, caption: e.target.value })
                }
                placeholder="Share your experience and what made this moment special..."
                className="min-h-[100px]"
                required
              />
            </div>

            <DialogFooter className="gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-primary hover:bg-primary/90"
              >
                <Share2 className="w-4 h-4 mr-2" />
                {isSubmitting ? "Uploading..." : "Share Photo"}
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
};
