import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";

interface Comment {
  id: string;
  user: {
    name: string;
    avatar: string;
    initials: string;
  };
  text: string;
  timestamp: string;
}

interface CommentSectionProps {
  postId: string;
  comments: Comment[];
  onAddComment: (postId: string, comment: Comment) => void;
  isVisible: boolean;
}

export const CommentSection = ({
  postId,
  comments,
  onAddComment,
  isVisible,
}: CommentSectionProps) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const router = useRouter();
  const [newComment, setNewComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!newComment.trim()) return;

    if (!user) {
      router.push("/auth");
      return;
    }

    setIsSubmitting(true);

    const comment: Comment = {
      id: Date.now().toString(),
      user: {
        name: user.profile?.display_name || user.profile?.username || "You",
        avatar:
          user.profile?.avatar_url ||
          `https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face`,
        initials: (user.profile?.display_name || user.profile?.username || "Y")
          .charAt(0)
          .toUpperCase(),
      },
      text: newComment,
      timestamp: "Just now",
    };

    onAddComment(postId, comment);
    setNewComment("");
    setIsSubmitting(false);

    toast({
      title: "Comment Added",
      description: "Your comment has been posted successfully",
    });
  };

  if (!isVisible) return null;

  return (
    <div className="space-y-4">
      <Separator />

      {/* Add Comment Form */}
      <div className="space-y-3">
        <Textarea
          placeholder="Write a comment..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          className="min-h-[80px] resize-none"
        />
        <div className="flex justify-end gap-2">
          <Button
            onClick={handleSubmit}
            disabled={!newComment.trim() || isSubmitting}
            size="sm"
          >
            {isSubmitting ? "Posting..." : "Post Comment"}
          </Button>
        </div>
      </div>

      {/* Comments List */}
      {comments.length > 0 && (
        <div className="space-y-4">
          <Separator />
          <h4 className="font-medium text-sm text-muted-foreground">
            Comments ({comments.length})
          </h4>

          <div className="space-y-4 max-h-60 overflow-y-auto">
            {comments.map((comment) => (
              <div key={comment.id} className="flex space-x-3">
                <Avatar className="w-8 h-8 flex-shrink-0">
                  <AvatarImage
                    src={comment.user.avatar}
                    alt={comment.user.name}
                  />
                  <AvatarFallback className="text-xs">
                    {comment.user.initials}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 min-w-0">
                  <div className="bg-muted rounded-lg p-3">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-sm">
                        {comment.user.name}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {comment.timestamp}
                      </span>
                    </div>
                    <p className="text-sm text-foreground">{comment.text}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
