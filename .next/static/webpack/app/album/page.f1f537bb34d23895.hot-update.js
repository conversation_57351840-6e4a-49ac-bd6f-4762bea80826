"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/album/page",{

/***/ "(app-pages-browser)/./src/components/CommentSection.tsx":
/*!*******************************************!*\
  !*** ./src/components/CommentSection.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CommentSection: () => (/* binding */ CommentSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CommentSection = (param)=>{\n    let { postId, comments, onAddComment, isVisible } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [newComment, setNewComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async ()=>{\n        var _user_profile, _user_profile1, _user_profile2, _user_profile3, _user_profile4;\n        if (!newComment.trim()) return;\n        if (!user) {\n            router.push(\"/auth\");\n            return;\n        }\n        setIsSubmitting(true);\n        const comment = {\n            id: Date.now().toString(),\n            user: {\n                name: ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.display_name) || ((_user_profile1 = user.profile) === null || _user_profile1 === void 0 ? void 0 : _user_profile1.username) || \"You\",\n                avatar: ((_user_profile2 = user.profile) === null || _user_profile2 === void 0 ? void 0 : _user_profile2.avatar_url) || \"https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face\",\n                initials: (((_user_profile3 = user.profile) === null || _user_profile3 === void 0 ? void 0 : _user_profile3.display_name) || ((_user_profile4 = user.profile) === null || _user_profile4 === void 0 ? void 0 : _user_profile4.username) || \"Y\").charAt(0).toUpperCase()\n            },\n            text: newComment,\n            timestamp: \"Just now\"\n        };\n        onAddComment(postId, comment);\n        setNewComment(\"\");\n        setIsSubmitting(false);\n        toast({\n            title: \"Comment Added\",\n            description: \"Your comment has been posted successfully\"\n        });\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                        placeholder: \"Write a comment...\",\n                        value: newComment,\n                        onChange: (e)=>setNewComment(e.target.value),\n                        className: \"min-h-[80px] resize-none\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleSubmit,\n                            disabled: !newComment.trim() || isSubmitting,\n                            size: \"sm\",\n                            children: isSubmitting ? \"Posting...\" : \"Post Comment\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            comments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-sm text-muted-foreground\",\n                        children: [\n                            \"Comments (\",\n                            comments.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 max-h-60 overflow-y-auto\",\n                        children: comments.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                        className: \"w-8 h-8 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                src: comment.user.avatar,\n                                                alt: comment.user.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: comment.user.initials\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-muted rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: comment.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: comment.timestamp\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-foreground\",\n                                                    children: comment.text\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, comment.id, true, {\n                                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CommentSection, \"t0MF/kTFu4HYlBkT5HcFZolnyoQ=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c = CommentSection;\nvar _c;\n$RefreshReg$(_c, \"CommentSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CommentSection.tsx\n"));

/***/ })

});