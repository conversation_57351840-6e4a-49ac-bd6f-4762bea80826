"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/album/page",{

/***/ "(app-pages-browser)/./src/components/CommentSection.tsx":
/*!*******************************************!*\
  !*** ./src/components/CommentSection.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CommentSection: () => (/* binding */ CommentSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CommentSection = (param)=>{\n    let { postId, comments, onAddComment, isVisible } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [newComment, setNewComment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async ()=>{\n        var _user_profile, _user_profile1, _user_profile2, _user_profile3, _user_profile4;\n        if (!newComment.trim()) return;\n        if (!user) {\n            navigate(\"/auth\");\n            return;\n        }\n        setIsSubmitting(true);\n        const comment = {\n            id: Date.now().toString(),\n            user: {\n                name: ((_user_profile = user.profile) === null || _user_profile === void 0 ? void 0 : _user_profile.display_name) || ((_user_profile1 = user.profile) === null || _user_profile1 === void 0 ? void 0 : _user_profile1.username) || \"You\",\n                avatar: ((_user_profile2 = user.profile) === null || _user_profile2 === void 0 ? void 0 : _user_profile2.avatar_url) || \"https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face\",\n                initials: (((_user_profile3 = user.profile) === null || _user_profile3 === void 0 ? void 0 : _user_profile3.display_name) || ((_user_profile4 = user.profile) === null || _user_profile4 === void 0 ? void 0 : _user_profile4.username) || \"Y\").charAt(0).toUpperCase()\n            },\n            text: newComment,\n            timestamp: \"Just now\"\n        };\n        onAddComment(postId, comment);\n        setNewComment(\"\");\n        setIsSubmitting(false);\n        toast({\n            title: \"Comment Added\",\n            description: \"Your comment has been posted successfully\"\n        });\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                        placeholder: \"Write a comment...\",\n                        value: newComment,\n                        onChange: (e)=>setNewComment(e.target.value),\n                        className: \"min-h-[80px] resize-none\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleSubmit,\n                            disabled: !newComment.trim() || isSubmitting,\n                            size: \"sm\",\n                            children: isSubmitting ? \"Posting...\" : \"Post Comment\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            comments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-sm text-muted-foreground\",\n                        children: [\n                            \"Comments (\",\n                            comments.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 max-h-60 overflow-y-auto\",\n                        children: comments.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                        className: \"w-8 h-8 flex-shrink-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                src: comment.user.avatar,\n                                                alt: comment.user.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                className: \"text-xs\",\n                                                children: comment.user.initials\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-muted rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: comment.user.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: comment.timestamp\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-foreground\",\n                                                    children: comment.text\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, comment.id, true, {\n                                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Desktop/safari-tour-develop/src/components/CommentSection.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CommentSection, \"t0MF/kTFu4HYlBkT5HcFZolnyoQ=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c = CommentSection;\nvar _c;\n$RefreshReg$(_c, \"CommentSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CommentSection.tsx\n"));

/***/ })

});