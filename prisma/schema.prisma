// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Profile {
  id           String   @id @default(cuid())
  userId       String   @unique @map("user_id")
  username     String?
  displayName  String?  @map("display_name")
  avatarUrl    String?  @map("avatar_url")
  bio          String?
  role         String   @default("user")
  isSeller     Boolean? @default(false) @map("is_seller")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  posts     Post[]
  comments  Comment[]
  likes     Like[]
  reviews   Review[]

  @@map("profiles")
}

model Post {
  id            String   @id @default(cuid())
  profileId     String   @map("profile_id")
  tour          String
  imageUrl      String   @map("image_url")
  caption       String
  location      String?
  likesCount    Int      @default(0) @map("likes_count")
  commentsCount Int      @default(0) @map("comments_count")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  profile  Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)
  comments Comment[]
  likes    Like[]

  @@map("posts")
}

model Comment {
  id        String   @id @default(cuid())
  postId    String   @map("post_id")
  profileId String   @map("profile_id")
  text      String
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  post    Post    @relation(fields: [postId], references: [id], onDelete: Cascade)
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@map("comments")
}

model Like {
  id         String   @id @default(cuid())
  profileId  String   @map("profile_id")
  targetId   String   @map("target_id")
  targetType String   @map("target_type")
  createdAt  DateTime @default(now()) @map("created_at")

  // Relations
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)
  post    Post?   @relation(fields: [targetId], references: [id], onDelete: Cascade)

  @@unique([profileId, targetId, targetType])
  @@map("likes")
}

model Product {
  id                   String    @id @default(cuid())
  name                 String
  description          String
  price                Float
  priceMin             Float?    @map("price_min")
  priceMax             Float?    @map("price_max")
  priceType            String?   @map("price_type")
  category             String
  imageUrl             String    @map("image_url")
  inStock              Boolean   @default(true) @map("in_stock")
  sellerId             String    @map("seller_id")
  discountPercentage   Float?    @map("discount_percentage")
  promotionText        String?   @map("promotion_text")
  promotionExpiresAt   DateTime? @map("promotion_expires_at")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")

  @@map("products")
}

model Review {
  id            String   @id @default(cuid())
  profileId     String   @map("profile_id")
  tour          String
  rating        Int
  title         String
  content       String
  helpful       Boolean  @default(false)
  likesCount    Int      @default(0) @map("likes_count")
  commentsCount Int      @default(0) @map("comments_count")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@map("reviews")
}
